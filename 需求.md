需求背景

这个项目我采用的技术栈是​​Rust + Tauri + React​， 我的需求背景是：我是一个游戏程序，我要和一个不懂cocos的游戏美术合作开发一个游戏，我希望能开发一个软件协同这个过程
需求是：
界面：
1. 有一个登录界面，界面有登录账号，登录密码，账号密码是我部署在服务器上的服务，gitea自己部署的一个仓库管理，校验是发送账号密码去校验，如果错误提示错误，正确则保留账号密码在本地方便后续提交
2. 有一个设置界面，需要登录后才能进行设置，
设置内容选项有：
· 自动设置是否开机启动软件
· 设置微信/飞书/qq 方便用于信息通知（只能选择1项，会检测对应配置是否填写了，如果没有填写授权就不进行通知）
· 默认的工作路径（即自动克隆仓库的路径）
3. 有一个工作界面，元素列表显示当前打开的coco项目，点击对应选择框切换当前打开的对应cocos项目编辑器，如果没有打开的项目，则会显示一个下拉框，下拉这个账户在我创建的gitea仓库中加入的组织中可见的仓库列表，
功能：
· 这个软件有后台功能，关闭ui会自动在后台常驻，除非退出
· 能发布自动更新软件的功能（登录校验账户成功后开始检查软件更新）
· 自动注册表，功能是右键打开文件夹为cocos项目（快速根据配置打开对应cocos编辑器进行开发，前提要通过子账号验证，否则会弹出软件要求进行登录）
· 自动注册表，功能是克隆cocos项目（也是会校验gitea账户，然后快速弹出软件，并打开工作界面，然后自动创建一个item,）
· 登录后会去申请可以创建克隆的项目，如果失败则可以选择的账户为空（不可创建打开）
· 选择一个有权限的仓库后，会首先检测工作路径是否有对应已经拷贝克隆的仓库，如果没有则会先进行git拷贝工程（如果没有安装git&node，则会自动/弹出安装git&node），等这些都搞完后，会检测对应要打开的cocosCreator项目对应的cocos编辑器是否有安装，如果没有安装会弹出cocos面板，让用户自己安装，然后提示用户自己重新尝试打开， 然后等这一切都检测完后，需要检测用户选择打开的项目git当前分支是否有可以拉取的，如果有则先进行拉取，等完成后，再打开这个项目，并显示在这个软件中，然后这个软件则自动最小化常驻后台，
· 退出cocos打开的项目会提示用户是否自动一键add所有修改，然后push (可以后台设置对应参数)
· 自动监听打开项目的仓库是否有最新的git提交（当前分支），如果有测提示用户进行拉取，同意则自动关闭打开的对应编辑器，然后自动开始拉取项目同步，显示ui，完成后则重新自动打开项目


整理的需求：
一、用户认证模块
登录界面
组件：账号输入框、密码输入框、登录按钮、错误提示区
流程：
提交凭证到自部署Gitea服务验证（Rust端使用reqwest调用Gitea API）
成功后将凭证加密存储（使用tauri-plugin-store）
失败时显示具体错误原因（如"账号不存在"、"密码错误"）
凭证管理
自动加载本地存储的凭证（启动时检查）
支持注销功能（清除本地凭证）

二、设置模块（需登录后访问）
系统设置
开机自启：调用tauri-plugin-autostart
默认工作路径：文件夹选择器组件（Rust端集成tauri-plugin-dialog）
通知设置
单选按钮组（微信/飞书/QQ）
动态表单：选择平台后显示对应配置项（如微信Webhook URL）
实时验证：点击"测试通知"按钮发送验证消息
Git行为设置
退出时自动提交：开关按钮（默认开启）
自定义提交消息模板：文本输入框

三、核心工作流模块
仓库管理
图表
代码
graph TD
    A[选择仓库] --> B{本地是否存在？}
    B -->|否| C[安装Git/Node检测]
    C --> D[执行git clone]
    B -->|是| E[检查Cocos编辑器]
    E --> F{版本匹配？}
    F -->|否| G[打开安装面板]
    F -->|是| H[git pull最新代码]
    H --> I[启动Cocos Creator]
关键功能
项目列表

实时显示已打开项目状态（运行中/已关闭）

项目卡片包含：仓库名、分支状态、最后提交时间

编辑器集成

动态检测Cocos安装路径（Windows注册表/macOS应用目录）

通过std::process::Command启动编辑器

进程监控：使用sysinfo包跟踪编辑器状态

自动化Git操作

项目关闭时：

自动git add . + git commit -m <模板>

冲突处理：生成差异报告供用户决策

定时轮询：每10分钟检查远程更新（通过cron调度）

四、系统集成模块
右键菜单注册（Windows/macOS）

使用tauri-plugin-shell注册系统命令

功能项：

"作为Cocos项目打开" → 启动软件并加载项目

"克隆Cocos仓库" → 唤起仓库选择界面

后台服务

系统托盘支持（tauri-plugin-tray）

事件监听：

编辑器进程退出触发提交

网络状态变化重试失败操作

自动更新

集成tauri-plugin-updater

登录成功后检查版本差异

支持静默后台更新

五、异常处理机制
依赖检测

Git未安装：引导下载安装包（调用tauri::api::shell::open）

Node.js缺失：提供版本管理工具链接

Cocos版本不匹配：显示推荐版本下载入口

网络错误处理

Gitea连接失败：缓存操作队列

通知发送失败：本地日志记录+重试机制

冲突解决

可视化差异对比（集成react-diff-viewer）

手动解决模式：暂停自动同步流程

六、技术实现要点
Rust后端

rust
// 示例：Gitea认证处理
async fn gitea_login(account: &str, password: &str) -> Result<AuthToken> {
    let client = reqwest::Client::new();
    let resp = client.post("https://your-gitea.com/api/v1/user")
        .basic_auth(account, Some(password))
        .send()
        .await?;
    
    if resp.status().is_success() {
        let token = generate_secure_token(); // 生成JWT
        store_credentials(token); // 加密存储
        Ok(token)
    } else {
        Err(anyhow!("Authentication failed"))
    }
}
React前端架构

jsx
// 仓库选择组件示例
const RepoSelector = ({ repos }) => {
  const [selected, setSelected] = useState(null);
  
  return (
    <div className="repo-list">
      {repos.map(repo => (
        <RepoCard 
          key={repo.id}
          repo={repo}
          onSelect={() => setSelected(repo)}
          isActive={selected?.id === repo.id}
        />
      ))}
      <button onClick={() => invoke('clone_repo', { repo: selected })}>
        克隆并打开
      </button>
    </div>
  );
};
进程管理

rust
// 监控Cocos进程状态
fn monitor_creator_process(pid: u32) {
    thread::spawn(move || {
        let mut sys = System::new();
        loop {
            sys.refresh_processes();
            if !sys.process(Pid::from(pid as usize)).is_some() {
                emit_event("editor_closed", pid); // 通知前端
                break;
            }
            sleep(Duration::from_secs(5));
        }
    });
}
七、安全增强设计
凭证存储：使用操作系统安全存储（Keychain/Keyring）

通信加密：Tauri默认的IPC通道加密 + HTTPS

权限隔离：

普通操作使用用户权限

安装操作需要显式提权（sudo/UAC弹窗）

八、部署方案
Gitea服务要求

开启API访问（需配置访问令牌）

组织仓库可见性设置为"公开"或"组织内可见"

软件分发

Windows：NSIS安装包（注册表写入）

macOS：dmg包（自动注册服务）

更新服务器：配置Tauri updater endpoint

