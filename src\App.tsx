import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { invoke } from '@tauri-apps/api/core';
import LoginPage from './pages/LoginPage';
import SettingsPage from './pages/SettingsPage';
import WorkspacePage from './pages/WorkspacePage';
import CocosManagerPage from './pages/CocosManagerPage';
import QuickLoginPage from './components/QuickLoginPage';

const App = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isAuthLoading, setIsAuthLoading] = useState(true);
  const [hasStoredCredentials, setHasStoredCredentials] = useState(false);
  const [showFullLogin, setShowFullLogin] = useState(false);

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        // Check if we're in Tauri environment
        if (typeof window !== 'undefined' && window.__TAURI__) {
          const credentials = await invoke('load_gitea_credentials');
          if (credentials) {
            setHasStoredCredentials(true);
            // 不自动登录，让用户选择
          } else {
            setHasStoredCredentials(false);
            setShowFullLogin(true);
          }
        } else {
          // In browser environment, check localStorage for demo credentials
          console.log('Running in browser environment, checking localStorage');
          const storedCredentials = localStorage.getItem('demo_credentials');
          if (storedCredentials) {
            setHasStoredCredentials(true);
            // 不自动登录，让用户选择
          } else {
            setHasStoredCredentials(false);
            setShowFullLogin(true);
          }
        }
      } catch (err) {
        console.error('检查认证状态失败:', err);
        setIsAuthenticated(false);
        setShowFullLogin(true);
      } finally {
        setIsAuthLoading(false);
      }
    };
    checkAuthStatus();
  }, []);

  const handleLogin = () => {
    setIsAuthenticated(true);
    // 在浏览器环境中保存登录状态到localStorage
    if (typeof window !== 'undefined' && !window.__TAURI__) {
      localStorage.setItem('demo_credentials', JSON.stringify({
        account: 'demo',
        timestamp: Date.now()
      }));
    }
  };

  const handleLogout = async () => {
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        await invoke('clear_gitea_credentials');
      } else {
        // 在浏览器环境中清除localStorage
        localStorage.removeItem('demo_credentials');
      }
      setIsAuthenticated(false);
      setHasStoredCredentials(false);
      setShowFullLogin(true);
    } catch (err) {
      console.error('注销失败:', err);
    }
  };

  if (isAuthLoading) {
    return <div>加载认证状态...</div>; // 或者一个加载指示器
  }

  const renderLoginComponent = () => {
    if (hasStoredCredentials && !showFullLogin) {
      return (
        <QuickLoginPage
          onLogin={handleLogin}
          onShowFullLogin={() => setShowFullLogin(true)}
        />
      );
    } else {
      return <LoginPage onLogin={handleLogin} />;
    }
  };

  return (
    <Router>
      <Routes>
        <Route
          path="/"
          element={
            isAuthenticated ?
              <Navigate to="/workspace" /> :
              renderLoginComponent()
          }
        />
        <Route
          path="/settings"
          element={
            isAuthenticated ?
              <SettingsPage onLogout={handleLogout} /> : // 传递注销函数给设置页面
              <Navigate to="/" />
          }
        />
        <Route
          path="/workspace"
          element={
            isAuthenticated ?
              <WorkspacePage onLogout={handleLogout} /> : // 传递注销函数给工作页面
              <Navigate to="/" />
          }
        />
        <Route
          path="/cocos-manager"
          element={
            isAuthenticated ?
              <CocosManagerPage onLogout={handleLogout} /> :
              <Navigate to="/" />
          }
        />
      </Routes>
    </Router>
  );
};

export default App;
