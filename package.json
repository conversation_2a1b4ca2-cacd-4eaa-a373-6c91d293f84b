{"name": "cocos-initiator", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri build"}, "dependencies": {"@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-opener": "^2", "@types/react-router-dom": "^5.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.8", "@tauri-apps/cli": "^2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "typescript": "~5.6.2", "vite": "^6.0.3"}}