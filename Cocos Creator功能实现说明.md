# Cocos Creator 检测和启动功能实现说明

## 新增功能概述

基于`完成总结.md`中的待实现功能，我已经实现了以下核心功能：

### 1. Cocos Creator 检测和启动 ✅
- **自动检测系统中安装的Cocos Creator版本**
- **智能版本匹配和启动**
- **跨平台支持（Windows、macOS、Linux）**

### 2. 进程监控 ✅
- **实时监控Cocos Creator进程状态**
- **自动更新项目运行状态**
- **进程异常检测和处理**

### 3. 项目版本管理 ✅
- **自动检测项目所需的Cocos Creator版本**
- **版本匹配和推荐**
- **多版本管理支持**

## 技术实现详情

### 后端实现 (Rust)

#### 新增数据结构
```rust
// Cocos Creator信息
pub struct CocosCreatorInfo {
    pub version: String,
    pub path: String,
    pub executable: String,
}

// 扩展的项目信息
pub struct Project {
    // ... 原有字段
    pub cocos_version: Option<String>, // Cocos Creator版本
    pub process_id: Option<u32>, // 进程ID
}

// 应用状态管理
pub struct AppState {
    pub config: Mutex<AppConfig>,
    pub running_processes: Mutex<HashMap<String, Child>>, // 进程管理
}
```

#### 新增命令函数

1. **`detect_cocos_creators()`** - 检测已安装的Cocos Creator版本
   - 扫描常见安装路径
   - 提取版本信息
   - 跨平台路径处理

2. **`get_project_cocos_version(project_path)`** - 获取项目所需版本
   - 解析project.json文件
   - 提取引擎版本信息

3. **`launch_cocos_creator(project_id, creator_path)`** - 启动Cocos Creator
   - 智能版本选择
   - 进程管理
   - 状态更新

4. **`check_project_process_status(project_id)`** - 检查进程状态
   - 实时进程监控
   - 状态同步

5. **`stop_project_process(project_id)`** - 停止进程
   - 优雅终止进程
   - 状态清理

6. **`get_running_projects_status()`** - 获取所有项目运行状态
   - 批量状态检查
   - 自动状态更新

### 前端实现 (React + TypeScript)

#### 更新的组件

1. **ProjectCard组件增强**
   - 显示Cocos Creator版本信息
   - 进程状态实时显示
   - 多版本启动选择
   - 进程控制按钮

2. **新增CocosManagerPage页面**
   - 显示所有已安装的Cocos Creator版本
   - 管理运行中的项目
   - 版本启动控制
   - 进程监控面板

3. **WorkspacePage增强**
   - 使用新的状态获取API
   - 添加Cocos管理页面链接
   - 实时状态更新

## 功能特性

### 智能版本检测
- **自动扫描**：扫描系统常见安装路径
- **版本提取**：从路径名和配置文件中提取版本信息
- **跨平台支持**：Windows、macOS、Linux不同路径处理

### 智能版本匹配
- **项目版本检测**：自动读取project.json中的引擎版本
- **版本匹配**：优先选择匹配的Cocos Creator版本
- **降级处理**：如无匹配版本，选择最新可用版本

### 进程管理
- **进程启动**：使用系统命令启动Cocos Creator
- **进程监控**：实时检查进程运行状态
- **进程控制**：支持优雅停止进程
- **状态同步**：自动更新项目运行状态

### 用户界面
- **版本选择器**：支持手动选择特定版本启动
- **状态指示器**：清晰显示项目和进程状态
- **操作按钮**：启动、停止、管理等操作
- **实时更新**：自动刷新状态信息

## 使用流程

### 1. 版本检测
1. 应用启动时自动检测已安装的Cocos Creator版本
2. 在Cocos管理页面查看所有可用版本
3. 支持手动刷新检测结果

### 2. 项目启动
1. 在工作区选择项目
2. 点击"启动Cocos Creator"按钮
3. 系统自动匹配合适的版本启动
4. 或手动选择特定版本启动

### 3. 进程监控
1. 启动后项目状态自动更新为"运行中"
2. 显示进程ID和版本信息
3. 实时监控进程状态
4. 进程异常时自动更新状态

### 4. 进程管理
1. 在项目卡片或Cocos管理页面查看运行中的项目
2. 点击"停止"按钮终止进程
3. 系统自动清理进程信息和更新状态

## 错误处理

### 检测失败处理
- 无Cocos Creator安装时显示提示信息
- 版本检测失败时提供手动刷新选项
- 路径访问权限问题的友好提示

### 启动失败处理
- 版本不匹配时的降级处理
- 启动失败时回退到文件夹打开方式
- 详细的错误信息显示

### 进程异常处理
- 进程意外终止的检测和状态更新
- 进程无响应时的强制终止选项
- 进程管理异常的恢复机制

## 配置要求

### 系统要求
- Windows: Cocos Creator安装在标准路径
- macOS: Cocos Creator安装在Applications目录
- Linux: Cocos Creator安装在用户目录或系统目录

### 权限要求
- 读取Cocos Creator安装目录的权限
- 启动外部进程的权限
- 访问项目文件夹的权限

## 后续扩展

### 计划中的功能
1. **系统托盘集成** - 后台运行和快速访问
2. **右键菜单注册** - 系统右键菜单集成
3. **通知推送** - 项目状态变化通知
4. **自动更新** - 应用自动更新机制

### 可能的增强
1. **版本下载** - 自动下载缺失的Cocos Creator版本
2. **项目模板** - 内置项目模板和快速创建
3. **构建集成** - 集成项目构建和发布功能
4. **性能监控** - 监控Cocos Creator性能和资源使用

## 测试建议

### 功能测试
1. 测试不同操作系统下的版本检测
2. 测试多版本Cocos Creator环境
3. 测试项目启动和进程管理
4. 测试异常情况的处理

### 用户体验测试
1. 界面响应性和流畅度
2. 错误信息的清晰度
3. 操作流程的直观性
4. 状态更新的及时性

这个实现大大增强了Cocos Initiator的核心功能，使其真正成为一个实用的Cocos Creator项目管理工具。
