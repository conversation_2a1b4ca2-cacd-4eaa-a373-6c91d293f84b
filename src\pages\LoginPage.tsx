import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';

interface LoginForm {
  username: string;
  password: string;
}

interface LoginPageProps {
  onLogin: () => void;
}

const LoginPage: React.FC<LoginPageProps> = ({ onLogin }) => {
  const [form, setForm] = useState<LoginForm>({ username: '', password: '' });
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        const appConfig = await invoke<any>('load_app_config');
        await invoke<string>('gitea_login_command', {
          giteaApiBaseUrl: appConfig.gitea_api_base_url,
          account: form.username,
          password: form.password
        });
      } else {
        // Mock login for browser environment
        if (form.username === 'demo' && form.password === 'demo') {
          console.log('Mock login successful');
        } else {
          throw new Error('演示模式：请使用用户名 "demo" 和密码 "demo"');
        }
      }

      console.log('登录成功');
      onLogin();

    } catch (err: any) {
      setError('认证失败: ' + (err.message || err));
    } finally {
      setIsLoading(false);
    }
  };

  // 移除自动登录逻辑，让App.tsx处理快速登录

  const handleLogout = async () => {
    setIsLoading(true);
    setError(null);
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        await invoke('clear_gitea_credentials');
      }
      console.log('已注销');
    } catch (err: any) {
      setError('注销失败: ' + (err.message || err));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
            <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Cocos Initiator
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            请登录您的 Gitea 账户以继续
          </p>
          {typeof window !== 'undefined' && !window.__TAURI__ && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-700">
                <strong>演示模式：</strong>使用用户名 "demo" 和密码 "demo" 进行登录
              </p>
            </div>
          )}
        </div>

        <div className="card">
          {error && (
            <div className="error-message mb-4">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
                账号
              </label>
              <input
                id="username"
                type="text"
                value={form.username}
                onChange={(e) => setForm({ ...form, username: e.target.value })}
                className="input-field"
                placeholder="请输入您的 Gitea 用户名"
                required
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                密码
              </label>
              <input
                id="password"
                type="password"
                value={form.password}
                onChange={(e) => setForm({ ...form, password: e.target.value })}
                className="input-field"
                placeholder="请输入您的密码"
                required
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary w-full"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  登录中...
                </div>
              ) : (
                '登录'
              )}
            </button>
          </form>

          <div className="mt-4 pt-4 border-t border-gray-200">
            <button
              onClick={handleLogout}
              disabled={isLoading}
              className="btn-secondary w-full"
            >
              清除本地凭证
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;