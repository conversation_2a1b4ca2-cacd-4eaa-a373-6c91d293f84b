import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Link } from 'react-router-dom';
import ProjectCard from '../components/ProjectCard';

interface Project {
  id: string;
  name: string;
  path: string;
  status: string; // 'running' | 'closed' | 'opened_folder'
  last_commit_time: string;
  repository_url: string;
  cocos_version?: string;
  process_id?: number;
}

interface GiteaRepository {
  id: number;
  name: string;
  full_name: string;
  description?: string;
  clone_url: string;
  ssh_url: string;
}

interface WorkspacePageProps {
  onLogout: () => void;
}

const WorkspacePage: React.FC<WorkspacePageProps> = ({ onLogout }) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [repos, setRepos] = useState<GiteaRepository[]>([]);
  const [selectedRepo, setSelectedRepo] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchProjects();
    fetchRepositories();

    // 设置定时刷新项目状态（每30秒）
    const interval = setInterval(() => {
      if (!isLoading) {
        fetchProjects();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [isLoading]);

  const fetchProjects = async () => {
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        // 使用新的获取运行状态的命令
        const projectList = await invoke<Project[]>('get_running_projects_status');
        setProjects(projectList);
      } else {
        // Mock data for browser environment
        const mockProjects: Project[] = [
          {
            id: '1',
            name: 'Sample Cocos Project',
            path: '/path/to/project',
            status: 'closed',
            last_commit_time: '2024-01-15 10:30:00',
            repository_url: 'https://example.com/repo.git',
            cocos_version: '3.8.2'
          }
        ];
        setProjects(mockProjects);
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      setError('获取项目列表失败');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRepositories = async () => {
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        const repoList = await invoke<GiteaRepository[]>('fetch_gitea_repositories');
        setRepos(repoList);
      } else {
        // Mock data for browser environment
        const mockRepos: GiteaRepository[] = [
          {
            id: 1,
            name: 'cocos-game-1',
            full_name: 'user/cocos-game-1',
            description: 'Sample Cocos Game 1',
            clone_url: 'https://example.com/cocos-game-1.git',
            ssh_url: '***************:user/cocos-game-1.git'
          },
          {
            id: 2,
            name: 'cocos-game-2',
            full_name: 'user/cocos-game-2',
            description: 'Sample Cocos Game 2',
            clone_url: 'https://example.com/cocos-game-2.git',
            ssh_url: '***************:user/cocos-game-2.git'
          }
        ];
        setRepos(mockRepos);
      }
    } catch (error) {
      console.error('获取仓库列表失败:', error);
      setError('获取仓库列表失败');
    }
  };

  const handleOpenProject = async (projectId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      if (typeof window !== 'undefined' && window.__TAURI__) {
        await invoke('open_project', { projectId });
        console.log('项目打开成功');
        // 刷新项目列表以更新状态
        await fetchProjects();
      } else {
        console.log('Mock: 打开项目:', projectId);
      }
    } catch (error: any) {
      console.error('打开项目失败:', error);
      setError(error.message || '打开项目失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveProject = async (projectId: string) => {
    if (!confirm('确定要从列表中移除这个项目吗？这不会删除项目文件。')) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      if (typeof window !== 'undefined' && window.__TAURI__) {
        await invoke('remove_project', { projectId });
        console.log('项目移除成功');
        await fetchProjects();
      } else {
        console.log('Mock: 移除项目:', projectId);
        // Mock removal for browser environment
        setProjects(prev => prev.filter(p => p.id !== projectId));
      }
    } catch (error: any) {
      console.error('移除项目失败:', error);
      setError(error.message || '移除项目失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloneAndOpen = async () => {
    if (!selectedRepo) return;

    try {
      setIsLoading(true);
      setError(null);

      const selectedRepoObj = repos.find(repo => repo.id.toString() === selectedRepo);
      if (!selectedRepoObj) {
        throw new Error('未找到选中的仓库');
      }

      console.log('克隆并打开仓库:', selectedRepoObj.name);

      if (typeof window !== 'undefined' && window.__TAURI__) {
        // 调用后端命令来克隆仓库
        const newProject = await invoke<Project>('clone_repository', {
          repoId: selectedRepoObj.id
        });
        console.log('克隆成功:', newProject);
      } else {
        // Mock implementation for browser environment
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      await fetchProjects();
      setSelectedRepo(''); // 重置选择
    } catch (error: any) {
      console.error('克隆并打开失败:', error);
      setError(error.message || '克隆并打开失败');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-gray-900">Cocos 工作区</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/cocos-manager" className="btn-secondary">
                Cocos管理
              </Link>
              <Link to="/settings" className="btn-secondary">
                设置
              </Link>
              <button onClick={onLogout} className="btn-secondary">
                注销
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {error && (
          <div className="error-message mb-6">
            {error}
          </div>
        )}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Projects Section */}
          <div className="lg:col-span-2">
            <div className="card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">已打开的项目</h2>
                <button
                  onClick={fetchProjects}
                  className="btn-secondary text-sm"
                  disabled={isLoading}
                >
                  刷新
                </button>
              </div>

              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <svg className="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span className="ml-2 text-gray-600">加载中...</span>
                </div>
              ) : projects.length === 0 ? (
                <div className="text-center py-12">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">没有打开的项目</h3>
                  <p className="mt-1 text-sm text-gray-500">开始克隆一个仓库来创建您的第一个项目</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {projects.map(project => (
                    <ProjectCard
                      key={project.id}
                      project={project}
                      onOpen={handleOpenProject}
                      onRemove={handleRemoveProject}
                      isLoading={isLoading}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Repository Selection */}
          <div className="lg:col-span-1">
            <div className="card">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">克隆新项目</h2>

              <div className="space-y-4">
                <div>
                  <label htmlFor="repo-select" className="block text-sm font-medium text-gray-700 mb-2">
                    选择仓库
                  </label>
                  <select
                    id="repo-select"
                    value={selectedRepo}
                    onChange={(e) => setSelectedRepo(e.target.value)}
                    disabled={isLoading || repos.length === 0}
                    className="input-field"
                  >
                    <option value="">-- 请选择仓库 --</option>
                    {repos.map(repo => (
                      <option key={repo.id} value={repo.id.toString()}>
                        {repo.name} - {repo.description || '无描述'}
                      </option>
                    ))}
                  </select>
                </div>

                <button
                  onClick={handleCloneAndOpen}
                  disabled={!selectedRepo || isLoading}
                  className="btn-primary w-full"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      处理中...
                    </div>
                  ) : (
                    '克隆并打开'
                  )}
                </button>

                {repos.length === 0 && (
                  <div className="text-center py-4">
                    <p className="text-sm text-gray-500">
                      没有可用的仓库。请检查您的 Gitea 权限设置。
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkspacePage;