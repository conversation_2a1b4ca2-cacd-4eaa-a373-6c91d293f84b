import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';

interface Project {
  id: string;
  name: string;
  path: string;
  status: string;
  last_commit_time: string;
  repository_url: string;
}

interface GitStatus {
  has_changes: boolean;
  current_branch: string;
  has_remote_updates: boolean;
  status_output: string;
}

interface ProjectCardProps {
  project: Project;
  onOpen: (projectId: string) => void;
  onRemove: (projectId: string) => void;
  isLoading: boolean;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ project, onOpen, onRemove, isLoading }) => {
  const [gitStatus, setGitStatus] = useState<GitStatus | null>(null);
  const [statusLoading, setStatusLoading] = useState(false);
  const [gitOperationLoading, setGitOperationLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  useEffect(() => {
    const checkGitStatus = async () => {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        try {
          setStatusLoading(true);
          const status = await invoke<GitStatus>('check_git_status', {
            projectPath: project.path
          });
          setGitStatus(status);
        } catch (error) {
          console.error('检查Git状态失败:', error);
          // 不显示错误，只是不显示Git状态
        } finally {
          setStatusLoading(false);
        }
      }
    };

    checkGitStatus();
  }, [project.path]);

  const getStatusColor = () => {
    if (project.status === 'running') return 'bg-green-100 text-green-800';
    return 'bg-gray-100 text-gray-800';
  };

  const getStatusText = () => {
    if (project.status === 'running') return '运行中';
    return '已关闭';
  };

  const handleCommit = async () => {
    try {
      setGitOperationLoading(true);
      setMessage(null);

      if (typeof window !== 'undefined' && window.__TAURI__) {
        const result = await invoke<string>('commit_project_changes', {
          projectPath: project.path,
          commitMessage: null // 使用默认提交消息
        });
        setMessage({ type: 'success', text: result });
        // 重新检查Git状态
        const status = await invoke<GitStatus>('check_git_status', {
          projectPath: project.path
        });
        setGitStatus(status);
      }
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || '提交失败' });
    } finally {
      setGitOperationLoading(false);
    }
  };

  const handlePull = async () => {
    try {
      setGitOperationLoading(true);
      setMessage(null);

      if (typeof window !== 'undefined' && window.__TAURI__) {
        const result = await invoke<string>('pull_project_updates', {
          projectPath: project.path
        });
        setMessage({ type: 'success', text: '拉取成功' });
        // 重新检查Git状态
        const status = await invoke<GitStatus>('check_git_status', {
          projectPath: project.path
        });
        setGitStatus(status);
      }
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || '拉取失败' });
    } finally {
      setGitOperationLoading(false);
    }
  };

  return (
    <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-medium text-gray-900">{project.name}</h3>
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      </div>

      {message && (
        <div className={`mb-3 p-2 rounded text-sm ${
          message.type === 'success'
            ? 'bg-green-50 text-green-700 border border-green-200'
            : 'bg-red-50 text-red-700 border border-red-200'
        }`}>
          {message.text}
        </div>
      )}

      <div className="space-y-2 text-sm text-gray-600 mb-4">
        <p><span className="font-medium">路径:</span> {project.path}</p>
        <p><span className="font-medium">最后提交:</span> {project.last_commit_time}</p>
        
        {gitStatus && (
          <div className="space-y-1">
            <p>
              <span className="font-medium">分支:</span> 
              <span className="ml-1 px-2 py-0.5 bg-blue-100 text-blue-800 rounded text-xs">
                {gitStatus.current_branch}
              </span>
            </p>
            
            {gitStatus.has_changes && (
              <p className="flex items-center">
                <span className="font-medium">状态:</span>
                <span className="ml-1 px-2 py-0.5 bg-yellow-100 text-yellow-800 rounded text-xs flex items-center">
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  有未提交更改
                </span>
              </p>
            )}
            
            {gitStatus.has_remote_updates && (
              <p className="flex items-center">
                <span className="font-medium">远程:</span>
                <span className="ml-1 px-2 py-0.5 bg-blue-100 text-blue-800 rounded text-xs flex items-center">
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                  </svg>
                  有远程更新
                </span>
              </p>
            )}
          </div>
        )}
        
        {statusLoading && (
          <p className="flex items-center text-gray-500">
            <svg className="animate-spin w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            检查Git状态...
          </p>
        )}
      </div>

      <div className="space-y-2">
        {/* Git操作按钮 */}
        {gitStatus && (gitStatus.has_changes || gitStatus.has_remote_updates) && (
          <div className="flex space-x-2">
            {gitStatus.has_changes && (
              <button
                onClick={handleCommit}
                disabled={isLoading || gitOperationLoading}
                className="btn-secondary text-sm flex-1"
                title="提交所有更改"
              >
                {gitOperationLoading ? '提交中...' : '提交更改'}
              </button>
            )}
            {gitStatus.has_remote_updates && (
              <button
                onClick={handlePull}
                disabled={isLoading || gitOperationLoading}
                className="btn-secondary text-sm flex-1"
                title="拉取远程更新"
              >
                {gitOperationLoading ? '拉取中...' : '拉取更新'}
              </button>
            )}
          </div>
        )}

        {/* 主要操作按钮 */}
        <div className="flex space-x-2">
          <button
            onClick={() => onOpen(project.id)}
            disabled={isLoading || gitOperationLoading}
            className="btn-primary text-sm flex-1"
          >
            {project.status === 'running' ? '查看项目' : '打开项目'}
          </button>
          <button
            onClick={() => onRemove(project.id)}
            disabled={isLoading || gitOperationLoading}
            className="btn-secondary text-sm text-red-600 hover:text-red-700 hover:bg-red-50 px-3"
            title="从列表中移除项目"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;
