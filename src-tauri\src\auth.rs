use serde::{Serialize, Deserialize};
use reqwest::Client;
use anyhow::{Result, anyhow};
use tauri_plugin_store::{StoreBuilder};
use tauri::{AppHandle};

const STORE_FILE_NAME: &str = ".gitea_credentials.dat";

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct GiteaCredentials {
    pub account: String,
    pub token: String, // Gitea Personal Access Token
}

// Tauri Command to handle Gitea login
#[tauri::command]
pub async fn gitea_login_command(app_handle: AppHandle, gitea_api_base_url: String, account: String, password: String) -> Result<String, String> {

    let client = Client::new();
    let url = format!("{}/users/{}/tokens", gitea_api_base_url, account);

    // Attempt to create a new personal access token
    // In a real application, you might want to check if a token already exists or use OAuth2
    let response = client.post(&url)
        .basic_auth(&account, Some(&password))
        .json(&serde_json::json!({
            "name": "cocos-initiator-token",
            "scopes": ["read:user", "read:repository", "write:repository"] // Adjust scopes as needed
        }))
        .send()
        .await
        .map_err(|e| format!("请求Gitea API失败: {}", e))?;

    if response.status().is_success() {
        let token_data: serde_json::Value = response.json().await.map_err(|e| format!("解析Gitea响应失败: {}", e))?;
        let token = token_data["sha1"].as_str().ok_or("无法获取Gitea Token")?.to_string();

        let credentials = GiteaCredentials {
            account,
            token,
        };

        // Store credentials securely
        store_gitea_credentials(app_handle, &credentials).await?;
        Ok("登录成功".to_string())
    } else {
        let error_text = response.text().await.unwrap_or_else(|_| "未知错误".to_string());
        Err(format!("Gitea认证失败: {}", error_text))
    }
}

// Store Gitea credentials using tauri-plugin-store
pub async fn store_gitea_credentials(app_handle: AppHandle, credentials: &GiteaCredentials) -> Result<(), String> {
    let store = StoreBuilder::new(&app_handle, STORE_FILE_NAME.parse().unwrap()).build();

    let creds_json = serde_json::to_string(credentials)
        .map_err(|e| format!("序列化凭证失败: {}", e))?;

    store.set("gitea_credentials", serde_json::Value::String(creds_json));
    store.save().await.map_err(|e| format!("保存凭证失败: {}", e))?;
    Ok(())
}

// Load Gitea credentials from tauri-plugin-store
#[tauri::command]
pub async fn load_gitea_credentials(app_handle: AppHandle) -> Result<Option<GiteaCredentials>, String> {
    let store = StoreBuilder::new(&app_handle, STORE_FILE_NAME.parse().unwrap()).build();

    if let Some(creds_value) = store.get("gitea_credentials") {
        let creds_json = creds_value.as_str().ok_or("凭证格式错误")?;
        let credentials: GiteaCredentials = serde_json::from_str(creds_json)
            .map_err(|e| format!("反序列化凭证失败: {}", e))?;
        Ok(Some(credentials))
    } else {
        Ok(None)
    }
}

// Clear Gitea credentials from tauri-plugin-store (logout)
#[tauri::command]
pub async fn clear_gitea_credentials(app_handle: AppHandle) -> Result<(), String> {
    let store = StoreBuilder::new(&app_handle, STORE_FILE_NAME.parse().unwrap()).build();
    store.delete("gitea_credentials");
    store.save().await.map_err(|e| format!("保存凭证失败: {}", e))?;
    Ok(())
}