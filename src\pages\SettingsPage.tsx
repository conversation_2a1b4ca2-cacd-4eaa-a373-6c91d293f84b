import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { useNavigate } from 'react-router-dom';
// its type definition may like this
import type * as app from '@tauri-apps/api/app';

declare global {
  interface Window {
    __TAURI__: {
      app?: typeof app;
      // ... the other tauri modules
    };
  }
}
interface SettingsPageProps {
  onLogout: () => void;
}

const SettingsPage: React.FC<SettingsPageProps> = ({ onLogout }) => {
  const navigate = useNavigate();
  const [autoStart, setAutoStart] = useState(false);
  const [notificationType, setNotificationType] = useState('none');
  const [wechatConfig, setWechatConfig] = useState('');
  const [feishuConfig, setFeishuConfig] = useState('');
  const [qqConfig, setQQConfig] = useState('');
  const [workPath, setWorkPath] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        const settings = await invoke<any>('load_settings');
        console.log('Loaded settings:', settings);
        // Update state with loaded settings
        setAutoStart(settings.auto_start || false);
        setNotificationType(settings.notification_type || 'none');
        setWechatConfig(settings.wechat_config || '');
        setFeishuConfig(settings.feishu_config || '');
        setQQConfig(settings.qq_config || '');
        setWorkPath(settings.work_path || '');
      } else {
        console.log('Running in browser environment, using default settings');
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  };

  const saveSettings = async () => {
    setIsLoading(true);
    setMessage(null);
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        await invoke('save_settings', {
          settings: {
            auto_start: autoStart,
            notifications: notificationType !== 'none',
            notification_type: notificationType,
            wechat_config: wechatConfig,
            feishu_config: feishuConfig,
            qq_config: qqConfig,
            work_path: workPath
          }
        });
      } else {
        // Mock save for browser environment
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      setMessage({ type: 'success', text: '设置保存成功' });

      // 保存成功后延迟返回工作区
      setTimeout(() => {
        navigate('/workspace');
      }, 1500);
    } catch (error) {
      setMessage({ type: 'error', text: `保存失败: ${error}` });
    } finally {
      setIsLoading(false);
    }
  };

  const selectFolder = async () => {
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        const path = await invoke('select_folder') as string;
        setWorkPath(path);
      } else {
        // Mock folder selection for browser environment
        setWorkPath('/mock/selected/path');
      }
    } catch (error) {
      console.error('Failed to select folder:', error);
    }
  };

  const testNotification = async () => {
    if (notificationType === 'none') {
      setMessage({ type: 'error', text: '请先选择通知方式' });
      return;
    }

    try {
      setIsLoading(true);
      setMessage(null);

      // Mock test notification
      if (typeof window !== 'undefined' && window.__TAURI__) {
        // TODO: 实现实际的通知测试
        console.log('测试通知:', notificationType);
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setMessage({ type: 'success', text: '测试通知发送成功！' });
    } catch (error: any) {
      setMessage({ type: 'error', text: `测试通知失败: ${error.message || error}` });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/workspace')}
                className="text-blue-600 hover:text-blue-700 p-1 rounded-lg hover:bg-blue-50 transition-colors"
                title="返回工作区"
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <h1 className="text-2xl font-bold text-gray-900">系统设置</h1>
            </div>
            <button
              onClick={onLogout}
              className="btn-secondary"
            >
              注销
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {message && (
          <div className={message.type === 'success' ? 'success-message' : 'error-message'}>
            {message.text}
          </div>
        )}

        <div className="space-y-6">
          {/* 系统设置 */}
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">系统设置</h3>
            <div className="flex items-center">
              <input
                id="auto-start"
                type="checkbox"
                checked={autoStart}
                onChange={(e) => setAutoStart(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="auto-start" className="ml-2 block text-sm text-gray-900">
                开机自动启动
              </label>
            </div>
          </div>

          {/* 通知设置 */}
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">通知设置</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="notification"
                    value="none"
                    checked={notificationType === 'none'}
                    onChange={() => setNotificationType('none')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-900">不启用</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="notification"
                    value="wechat"
                    checked={notificationType === 'wechat'}
                    onChange={() => setNotificationType('wechat')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-900">微信</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="notification"
                    value="feishu"
                    checked={notificationType === 'feishu'}
                    onChange={() => setNotificationType('feishu')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-900">飞书</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="notification"
                    value="qq"
                    checked={notificationType === 'qq'}
                    onChange={() => setNotificationType('qq')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-900">QQ</span>
                </label>
              </div>

              {notificationType !== 'none' && (
                <div className="mt-4">
                  {notificationType === 'wechat' && (
                    <div>
                      <label htmlFor="wechat-config" className="block text-sm font-medium text-gray-700 mb-2">
                        微信 Webhook URL
                      </label>
                      <input
                        id="wechat-config"
                        type="text"
                        value={wechatConfig}
                        onChange={(e) => setWechatConfig(e.target.value)}
                        className="input-field"
                        placeholder="请输入微信 Webhook URL"
                      />
                    </div>
                  )}
                  {notificationType === 'feishu' && (
                    <div>
                      <label htmlFor="feishu-config" className="block text-sm font-medium text-gray-700 mb-2">
                        飞书 Webhook URL
                      </label>
                      <input
                        id="feishu-config"
                        type="text"
                        value={feishuConfig}
                        onChange={(e) => setFeishuConfig(e.target.value)}
                        className="input-field"
                        placeholder="请输入飞书 Webhook URL"
                      />
                    </div>
                  )}
                  {notificationType === 'qq' && (
                    <div>
                      <label htmlFor="qq-config" className="block text-sm font-medium text-gray-700 mb-2">
                        QQ 通知配置
                      </label>
                      <input
                        id="qq-config"
                        type="text"
                        value={qqConfig}
                        onChange={(e) => setQQConfig(e.target.value)}
                        className="input-field"
                        placeholder="请输入 QQ 通知配置"
                      />
                    </div>
                  )}

                  {notificationType !== 'none' && (
                    <div className="mt-4">
                      <button
                        onClick={testNotification}
                        disabled={isLoading}
                        className="btn-secondary text-sm"
                      >
                        {isLoading ? '测试中...' : '测试通知'}
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* 工作路径设置 */}
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">工作路径设置</h3>
            <div className="flex space-x-4">
              <div className="flex-1">
                <input
                  type="text"
                  value={workPath}
                  onChange={(e) => setWorkPath(e.target.value)}
                  className="input-field"
                  placeholder="请输入默认工作路径"
                />
              </div>
              <button
                onClick={selectFolder}
                className="btn-secondary whitespace-nowrap"
              >
                选择文件夹
              </button>
            </div>
          </div>

          {/* 保存按钮 */}
          <div className="flex justify-end space-x-4">
            <button
              onClick={() => navigate('/workspace')}
              disabled={isLoading}
              className="btn-secondary"
            >
              取消
            </button>
            <button
              onClick={saveSettings}
              disabled={isLoading}
              className="btn-primary"
            >
              {isLoading ? '保存中...' : '保存设置'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;