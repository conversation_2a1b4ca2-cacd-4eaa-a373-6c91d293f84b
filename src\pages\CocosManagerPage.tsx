import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Link } from 'react-router-dom';

interface CocosCreatorInfo {
  version: string;
  path: string;
  executable: string;
}

interface Project {
  id: string;
  name: string;
  path: string;
  status: string;
  last_commit_time: string;
  repository_url: string;
  cocos_version?: string;
  process_id?: number;
}

interface CocosManagerPageProps {
  onLogout: () => void;
}

const CocosManagerPage: React.FC<CocosManagerPageProps> = ({ onLogout }) => {
  const [cocosCreators, setCocosCreators] = useState<CocosCreatorInfo[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (typeof window !== 'undefined' && window.__TAURI__) {
        const [creators, projectList] = await Promise.all([
          invoke<CocosCreatorInfo[]>('detect_cocos_creators'),
          invoke<Project[]>('get_running_projects_status')
        ]);
        setCocosCreators(creators);
        setProjects(projectList);
      } else {
        // Mock data for browser environment
        setCocosCreators([
          {
            version: '3.8.2',
            path: 'C:\\CocosCreator\\3.8.2',
            executable: 'C:\\CocosCreator\\3.8.2\\CocosCreator.exe'
          },
          {
            version: '3.7.4',
            path: 'C:\\CocosCreator\\3.7.4',
            executable: 'C:\\CocosCreator\\3.7.4\\CocosCreator.exe'
          }
        ]);
        setProjects([]);
      }
    } catch (error: any) {
      console.error('加载数据失败:', error);
      setError(error.message || '加载数据失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLaunchCocos = async (creatorPath: string, projectId?: string) => {
    try {
      setMessage(null);

      if (typeof window !== 'undefined' && window.__TAURI__) {
        if (projectId) {
          await invoke('launch_cocos_creator', {
            projectId,
            creatorPath
          });
          setMessage({ type: 'success', text: 'Cocos Creator启动成功' });
        } else {
          // 直接启动Cocos Creator（不打开项目）
          const { Command } = await import('@tauri-apps/plugin-shell');
          await Command.create('launch-cocos', [creatorPath]).execute();
          setMessage({ type: 'success', text: 'Cocos Creator启动成功' });
        }
        
        // 刷新项目状态
        setTimeout(() => {
          loadData();
        }, 2000);
      }
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'Cocos Creator启动失败' });
    }
  };

  const handleStopProcess = async (projectId: string) => {
    try {
      setMessage(null);

      if (typeof window !== 'undefined' && window.__TAURI__) {
        await invoke('stop_project_process', { projectId });
        setMessage({ type: 'success', text: '进程已停止' });
        
        // 刷新项目状态
        setTimeout(() => {
          loadData();
        }, 1000);
      }
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || '停止进程失败' });
    }
  };

  const getRunningProjects = () => {
    return projects.filter(p => p.status === 'running');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <div className="h-8 w-8 bg-purple-600 rounded-lg flex items-center justify-center">
                <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-gray-900">Cocos Creator 管理</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/workspace" className="btn-secondary">
                返回工作区
              </Link>
              <Link to="/settings" className="btn-secondary">
                设置
              </Link>
              <button onClick={onLogout} className="btn-secondary">
                注销
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {error && (
          <div className="error-message mb-6">
            {error}
          </div>
        )}

        {message && (
          <div className={`mb-6 p-4 rounded-md ${
            message.type === 'success'
              ? 'bg-green-50 text-green-700 border border-green-200'
              : 'bg-red-50 text-red-700 border border-red-200'
          }`}>
            {message.text}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Cocos Creator Versions */}
          <div className="card">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">已安装的版本</h2>
              <button
                onClick={loadData}
                className="btn-secondary text-sm"
                disabled={isLoading}
              >
                刷新
              </button>
            </div>

            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <svg className="animate-spin h-8 w-8 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span className="ml-2 text-gray-600">检测中...</span>
              </div>
            ) : cocosCreators.length === 0 ? (
              <div className="text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">未检测到Cocos Creator</h3>
                <p className="mt-1 text-sm text-gray-500">请确保已正确安装Cocos Creator</p>
              </div>
            ) : (
              <div className="space-y-4">
                {cocosCreators.map((creator, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-lg font-medium text-gray-900">
                        版本 {creator.version}
                      </h3>
                      <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs font-medium">
                        已安装
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">
                      <span className="font-medium">路径:</span> {creator.path}
                    </p>
                    <button
                      onClick={() => handleLaunchCocos(creator.executable)}
                      className="btn-primary text-sm w-full"
                    >
                      启动此版本
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Running Projects */}
          <div className="card">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">运行中的项目</h2>

            {getRunningProjects().length === 0 ? (
              <div className="text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">没有运行中的项目</h3>
                <p className="mt-1 text-sm text-gray-500">从工作区启动项目后会在这里显示</p>
              </div>
            ) : (
              <div className="space-y-4">
                {getRunningProjects().map(project => (
                  <div key={project.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-lg font-medium text-gray-900">{project.name}</h3>
                      <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium">
                        运行中
                      </span>
                    </div>
                    <div className="space-y-1 text-sm text-gray-600 mb-3">
                      {project.cocos_version && (
                        <p>
                          <span className="font-medium">版本:</span> {project.cocos_version}
                        </p>
                      )}
                      {project.process_id && (
                        <p>
                          <span className="font-medium">进程ID:</span> {project.process_id}
                        </p>
                      )}
                      <p>
                        <span className="font-medium">路径:</span> {project.path}
                      </p>
                    </div>
                    <button
                      onClick={() => handleStopProcess(project.id)}
                      className="btn-secondary text-sm w-full text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      停止项目
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CocosManagerPage;
