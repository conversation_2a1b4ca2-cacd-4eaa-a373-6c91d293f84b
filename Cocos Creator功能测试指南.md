# Cocos Creator 功能测试指南

## 测试环境准备

### 1. 系统要求
- **Windows**: Windows 10/11
- **macOS**: macOS 10.15+
- **Linux**: Ubuntu 18.04+ 或其他主流发行版

### 2. 依赖安装
```bash
# 安装Rust工具链
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 安装Node.js依赖
npm install

# 安装Tauri CLI
npm install -g @tauri-apps/cli
```

### 3. Cocos Creator安装
为了测试功能，建议安装多个版本的Cocos Creator：
- 下载并安装至少2个不同版本（如3.8.2和3.7.4）
- 确保安装在标准路径下

## 功能测试步骤

### 1. 版本检测测试

#### 启动应用
```bash
npm run tauri dev
```

#### 测试步骤
1. 登录应用后，点击"Cocos管理"按钮
2. 查看"已安装的版本"部分
3. 验证是否正确检测到所有已安装的版本
4. 点击"刷新"按钮测试重新检测功能

#### 预期结果
- 显示所有已安装的Cocos Creator版本
- 每个版本显示版本号和安装路径
- 刷新功能正常工作

### 2. 项目版本检测测试

#### 准备测试项目
1. 创建或获取一个Cocos Creator项目
2. 确保项目包含有效的`project.json`文件
3. 将项目克隆到工作区或手动添加

#### 测试步骤
1. 在工作区查看项目卡片
2. 检查是否显示项目的Cocos版本信息
3. 验证版本信息是否与project.json中的一致

#### 预期结果
- 项目卡片显示正确的Cocos Creator版本
- 版本信息与项目实际需求匹配

### 3. 智能启动测试

#### 测试场景1：版本匹配启动
1. 选择一个项目，其版本与已安装版本匹配
2. 点击"启动Cocos Creator"按钮
3. 观察启动过程和结果

#### 测试场景2：版本不匹配启动
1. 选择一个项目，其版本与已安装版本不完全匹配
2. 点击"启动Cocos Creator"按钮
3. 验证是否选择了最接近的版本

#### 测试场景3：手动版本选择
1. 点击"选择版本启动"按钮
2. 从版本列表中选择特定版本
3. 验证是否使用选定版本启动

#### 预期结果
- 自动选择合适的版本启动
- 手动选择功能正常工作
- 启动成功后项目状态更新为"运行中"

### 4. 进程监控测试

#### 测试步骤
1. 启动一个或多个Cocos Creator项目
2. 在工作区查看项目状态
3. 在Cocos管理页面查看"运行中的项目"
4. 验证进程ID显示
5. 手动关闭Cocos Creator，观察状态变化

#### 预期结果
- 项目状态正确显示为"运行中"
- 显示正确的进程ID
- 进程结束后状态自动更新为"已关闭"

### 5. 进程控制测试

#### 测试步骤
1. 启动一个Cocos Creator项目
2. 在项目卡片或Cocos管理页面点击"停止"按钮
3. 验证进程是否被正确终止
4. 检查项目状态是否更新

#### 预期结果
- 进程被成功终止
- 项目状态更新为"已关闭"
- 进程ID信息被清除

### 6. 错误处理测试

#### 测试场景1：无Cocos Creator安装
1. 临时重命名Cocos Creator安装目录
2. 重启应用并检查检测结果
3. 验证错误提示是否友好

#### 测试场景2：启动失败
1. 删除或损坏Cocos Creator可执行文件
2. 尝试启动项目
3. 验证错误处理和降级机制

#### 测试场景3：进程异常
1. 启动项目后手动终止Cocos Creator进程
2. 检查应用是否能检测到进程异常
3. 验证状态更新是否正确

#### 预期结果
- 显示友好的错误信息
- 提供合理的降级方案
- 异常情况下状态更新正确

## 性能测试

### 1. 启动性能
- 测量版本检测的时间
- 测量项目启动的时间
- 验证大量项目时的性能

### 2. 内存使用
- 监控应用内存使用情况
- 检查是否有内存泄漏
- 验证进程管理的资源使用

### 3. 响应性测试
- 测试UI响应性
- 验证长时间运行的稳定性
- 检查状态更新的及时性

## 跨平台测试

### Windows测试
- 测试不同Windows版本的兼容性
- 验证路径处理的正确性
- 测试权限相关功能

### macOS测试
- 测试.app包的处理
- 验证权限和安全设置
- 测试不同macOS版本

### Linux测试
- 测试不同发行版的兼容性
- 验证包管理器安装的Cocos Creator
- 测试权限和路径处理

## 用户体验测试

### 1. 界面测试
- 验证所有UI元素的可用性
- 测试响应式设计
- 检查视觉一致性

### 2. 工作流测试
- 测试完整的项目管理工作流
- 验证操作的直观性
- 检查错误恢复流程

### 3. 文档测试
- 验证帮助信息的准确性
- 测试工具提示的有用性
- 检查错误信息的清晰度

## 回归测试

### 1. 原有功能测试
- 验证登录功能仍然正常
- 测试Git集成功能
- 检查设置页面功能

### 2. 集成测试
- 测试新旧功能的集成
- 验证数据一致性
- 检查状态同步

## 测试报告

### 测试结果记录
- 记录每个测试用例的结果
- 标注发现的问题和bug
- 记录性能数据

### 问题分类
- **严重**: 影响核心功能的问题
- **一般**: 影响用户体验的问题
- **轻微**: 界面或提示相关的问题

### 改进建议
- 基于测试结果提出改进建议
- 优先级排序
- 实施计划

## 自动化测试

### 单元测试
```bash
# Rust后端测试
cd src-tauri
cargo test

# 前端测试
npm test
```

### 集成测试
- 编写端到端测试脚本
- 自动化UI测试
- 性能基准测试

通过这个全面的测试指南，可以确保Cocos Creator集成功能的质量和稳定性。
